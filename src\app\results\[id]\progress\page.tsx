'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Calendar,
  Activity,
  CheckCircle,
  Clock,
  Headphones,
  Eye,
  PenTool,
  MessageCircle
} from 'lucide-react';
import NavigationMenu from '@/components/results/NavigationMenu';

interface TestResult {
  id: string;
  testRegistrationId: string;
  testDate: string;
  listeningScore: number | null;
  listeningBandScore: number | null;
  readingScore: number | null;
  readingBandScore: number | null;
  writingTask1Score: number | null;
  writingTask2Score: number | null;
  writingBandScore: number | null;
  speakingFluencyScore: number | null;
  speakingLexicalScore: number | null;
  speakingGrammarScore: number | null;
  speakingPronunciationScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  status: 'pending' | 'completed' | 'verified';
  certificateSerial: string | null;
  certificateGenerated: boolean;
  aiFeedbackGenerated: boolean;
  createdAt: string;
  updatedAt: string;
  candidate: {
    id: string;
    fullName: string;
    nationality: string;
    photoUrl: string | null;
  };
  testRegistration: {
    candidateNumber: string;
    testDate: string;
    testCenter: string;
  };
}

export default function ProgressPage() {
  const params = useParams();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  useEffect(() => {
    fetchResult();
  }, [fetchResult]);

  const getProgressPercentage = (score: number | null, maxScore: number = 9) => {
    if (!score) return 0;
    return (score / maxScore) * 100;
  };

  const getProgressColor = (score: number | null) => {
    if (!score) return 'bg-gray-300';
    if (score >= 8) return 'bg-green-500';
    if (score >= 7) return 'bg-blue-500';
    if (score >= 6) return 'bg-yellow-500';
    if (score >= 5) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getStatusIcon = (score: number | null) => {
    if (!score) return <Clock className="h-5 w-5 text-gray-500" />;
    return <CheckCircle className="h-5 w-5 text-green-500" />;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading progress...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Unable to Load Progress</h3>
          <p className="text-gray-600 mb-6">{error || 'Result not found'}</p>
          <Link
            href="/search"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/search" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Search
              </Link>
              <Activity className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Progress Tracking</h1>
                <p className="text-gray-600">Module Performance Overview</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Navigation Menu */}
          <div className="lg:col-span-1">
            <NavigationMenu resultId={resultId} />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Overall Progress Summary */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Overall Progress Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">{result.overallBandScore || 'N/A'}</div>
                  <div className="text-sm text-gray-600">Overall Band Score</div>
                  <div className="mt-2">
                    {result.overallBandScore ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-500 mx-auto" />
                    )}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {result.status === 'completed' || result.status === 'verified' ? '100%' : '75%'}
                  </div>
                  <div className="text-sm text-gray-600">Test Completion</div>
                  <div className="mt-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    {new Date(result.testRegistration.testDate).toLocaleDateString()}
                  </div>
                  <div className="text-sm text-gray-600">Test Date</div>
                  <div className="mt-2">
                    <Calendar className="h-5 w-5 text-purple-500 mx-auto" />
                  </div>
                </div>
              </div>
            </div>

            {/* Module Progress */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Module Progress</h2>
              <div className="space-y-6">
                {/* Listening Progress */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Headphones className="h-6 w-6 text-blue-600 mr-3" />
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Listening</h3>
                        <p className="text-sm text-gray-600">Comprehension & Understanding</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(result.listeningBandScore)}
                      <span className="text-2xl font-bold text-blue-600">
                        {result.listeningBandScore || 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                    <div
                      className={`h-3 rounded-full ${getProgressColor(result.listeningBandScore)}`}
                      style={{ width: `${getProgressPercentage(result.listeningBandScore)}%` }}
                    ></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Raw Score:</span>
                      <span className="font-medium ml-2">{result.listeningScore || 'N/A'}/40</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Performance:</span>
                      <span className="font-medium ml-2">
                        {result.listeningBandScore && result.listeningBandScore >= 7 ? 'Excellent' :
                         result.listeningBandScore && result.listeningBandScore >= 6 ? 'Good' :
                         result.listeningBandScore && result.listeningBandScore >= 5 ? 'Adequate' : 'Needs Improvement'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Reading Progress */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Eye className="h-6 w-6 text-green-600 mr-3" />
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Reading</h3>
                        <p className="text-sm text-gray-600">Comprehension & Analysis</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(result.readingBandScore)}
                      <span className="text-2xl font-bold text-green-600">
                        {result.readingBandScore || 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                    <div
                      className={`h-3 rounded-full ${getProgressColor(result.readingBandScore)}`}
                      style={{ width: `${getProgressPercentage(result.readingBandScore)}%` }}
                    ></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Raw Score:</span>
                      <span className="font-medium ml-2">{result.readingScore || 'N/A'}/40</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Performance:</span>
                      <span className="font-medium ml-2">
                        {result.readingBandScore && result.readingBandScore >= 7 ? 'Excellent' :
                         result.readingBandScore && result.readingBandScore >= 6 ? 'Good' :
                         result.readingBandScore && result.readingBandScore >= 5 ? 'Adequate' : 'Needs Improvement'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Writing Progress */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <PenTool className="h-6 w-6 text-yellow-600 mr-3" />
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Writing</h3>
                        <p className="text-sm text-gray-600">Task Achievement & Language Use</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(result.writingBandScore)}
                      <span className="text-2xl font-bold text-yellow-600">
                        {result.writingBandScore || 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                    <div
                      className={`h-3 rounded-full ${getProgressColor(result.writingBandScore)}`}
                      style={{ width: `${getProgressPercentage(result.writingBandScore)}%` }}
                    ></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Task 1:</span>
                      <span className="font-medium ml-2">{result.writingTask1Score || 'N/A'}/9</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Task 2:</span>
                      <span className="font-medium ml-2">{result.writingTask2Score || 'N/A'}/9</span>
                    </div>
                  </div>
                </div>

                {/* Speaking Progress */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <MessageCircle className="h-6 w-6 text-purple-600 mr-3" />
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Speaking</h3>
                        <p className="text-sm text-gray-600">Fluency & Communication</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(result.speakingBandScore)}
                      <span className="text-2xl font-bold text-purple-600">
                        {result.speakingBandScore || 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                    <div
                      className={`h-3 rounded-full ${getProgressColor(result.speakingBandScore)}`}
                      style={{ width: `${getProgressPercentage(result.speakingBandScore)}%` }}
                    ></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Fluency:</span>
                      <span className="font-medium ml-2">{result.speakingFluencyScore || 'N/A'}/9</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Vocabulary:</span>
                      <span className="font-medium ml-2">{result.speakingLexicalScore || 'N/A'}/9</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Grammar:</span>
                      <span className="font-medium ml-2">{result.speakingGrammarScore || 'N/A'}/9</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Pronunciation:</span>
                      <span className="font-medium ml-2">{result.speakingPronunciationScore || 'N/A'}/9</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Timeline */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Test Timeline</h2>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">Test Registration</h4>
                    <p className="text-sm text-gray-600">Candidate #{result.testRegistration.candidateNumber}</p>
                  </div>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">Test Completion</h4>
                    <p className="text-sm text-gray-600">{new Date(result.testRegistration.testDate).toLocaleDateString()}</p>
                  </div>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">Results Available</h4>
                    <p className="text-sm text-gray-600">{new Date(result.createdAt).toLocaleDateString()}</p>
                  </div>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </div>
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-8 h-8 ${result.certificateGenerated ? 'bg-blue-600' : 'bg-gray-400'} text-white rounded-full flex items-center justify-center text-sm font-bold`}>4</div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">Certificate Generated</h4>
                    <p className="text-sm text-gray-600">
                      {result.certificateGenerated ? 'Available for download' : 'Pending generation'}
                    </p>
                  </div>
                  {result.certificateGenerated ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <Clock className="h-5 w-5 text-gray-500" />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
