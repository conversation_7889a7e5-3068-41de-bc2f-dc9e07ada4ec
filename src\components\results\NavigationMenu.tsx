'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Menu,
  Activity,
  MessageCircle,
  Award,
  BarChart3,
  ArrowLeft
} from 'lucide-react';

interface NavigationMenuProps {
  resultId: string;
  className?: string;
}

export default function NavigationMenu({ resultId, className = '' }: NavigationMenuProps) {
  const pathname = usePathname();

  const menuItems = [
    {
      href: `/results/${resultId}`,
      label: 'Overview',
      icon: Menu,
      description: 'Summary & Main Results'
    },
    {
      href: `/results/${resultId}/progress`,
      label: 'Progress',
      icon: Activity,
      description: 'Module Progress Tracking'
    },
    {
      href: `/results/${resultId}/feedback`,
      label: 'Feedback',
      icon: MessageCircle,
      description: 'Detailed Analysis & Tips'
    },
    {
      href: `/results/${resultId}/certificate`,
      label: 'Certificate',
      icon: Award,
      description: 'Official Certificate'
    }
  ];

  const isActive = (href: string) => {
    if (href === `/results/${resultId}`) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="flex items-center mb-6">
        <BarChart3 className="h-6 w-6 text-blue-600 mr-3" />
        <h2 className="text-lg font-semibold text-gray-900">Result Sections</h2>
      </div>

      <nav className="space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);

          return (
            <Link
              key={item.href}
              href={item.href}
              className={`block w-full p-4 rounded-lg transition-all duration-200 ${
                active
                  ? 'bg-blue-50 border-l-4 border-blue-500 text-blue-700'
                  : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'
              }`}
            >
              <div className="flex items-center">
                <Icon className={`h-5 w-5 mr-3 ${active ? 'text-blue-600' : 'text-gray-500'}`} />
                <div>
                  <div className={`font-medium ${active ? 'text-blue-900' : 'text-gray-900'}`}>
                    {item.label}
                  </div>
                  <div className={`text-sm ${active ? 'text-blue-600' : 'text-gray-500'}`}>
                    {item.description}
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </nav>

      <div className="mt-6 pt-6 border-t border-gray-200">
        <Link
          href="/search"
          className="flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Search
        </Link>
      </div>
    </div>
  );
}
