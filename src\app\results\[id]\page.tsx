'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeft,
  Calendar,
  MapPin,
  User,
  FileText,
  Activity,
  CheckCircle,
  Clock,
  AlertCircle,
  Award,
  MessageCircle,
  Headphones,
  Eye,
  PenTool
} from 'lucide-react';
import NavigationMenu from '@/components/results/NavigationMenu';
import ScoreChart from '@/components/charts/ScoreChart';
import PerformanceChart from '@/components/charts/PerformanceChart';

interface TestResult {
  id: string;
  testRegistrationId: string;
  testDate: string;
  listeningScore: number | null;
  listeningBandScore: number | null;
  readingScore: number | null;
  readingBandScore: number | null;
  writingTask1Score: number | null;
  writingTask2Score: number | null;
  writingBandScore: number | null;
  speakingFluencyScore: number | null;
  speakingLexicalScore: number | null;
  speakingGrammarScore: number | null;
  speakingPronunciationScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  status: 'pending' | 'completed' | 'verified';
  certificateSerial: string | null;
  certificateGenerated: boolean;
  aiFeedbackGenerated: boolean;
  createdAt: string;
  updatedAt: string;
  candidate: {
    id: string;
    fullName: string;
    nationality: string;
    photoUrl: string | null;
  };
  testRegistration: {
    candidateNumber: string;
    testDate: string;
    testCenter: string;
  };
  performanceMetrics: {
    averageScore: number | null;
    highestScore: number | null;
    lowestScore: number | null;
    scoreDistribution: {
      listening?: number | null;
      reading?: number | null;
      writing?: number | null;
      speaking?: number | null;
    };
  };
}

export default function PublicResultsPage() {
  const params = useParams();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  useEffect(() => {
    fetchResult();
  }, [fetchResult]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'verified':
        return <Award className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-800 bg-yellow-100';
      case 'completed':
        return 'text-green-800 bg-green-100';
      case 'verified':
        return 'text-blue-800 bg-blue-100';
      default:
        return 'text-gray-800 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your results...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Unable to Load Results</h3>
          <p className="text-gray-600 mb-6">{error || 'Result not found'}</p>
          <Link
            href="/search"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/search" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Search
              </Link>
              <FileText className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">IELTS Test Results</h1>
                <p className="text-gray-600">Results Overview</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <NavigationMenu resultId={resultId} />
          </div>

          <div className="lg:col-span-3">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-1 space-y-6">
                <div className="bg-white shadow rounded-lg p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Candidate Information</h2>
                  <div className="flex items-start space-x-4">
                    {result.candidate.photoUrl ? (
                      <Image
                        src={result.candidate.photoUrl}
                        alt={result.candidate.fullName}
                        width={80}
                        height={80}
                        className="rounded-lg object-cover"
                      />
                    ) : (
                      <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                        <User className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                    <div className="flex-1">
                      <h4 className="text-xl font-semibold text-gray-900">{result.candidate.fullName}</h4>
                      <div className="mt-2 space-y-2">
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-4 w-4 mr-2" />
                          {result.candidate.nationality}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Calendar className="h-4 w-4 mr-2" />
                          Test Date: {new Date(result.testRegistration.testDate).toLocaleDateString()}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-4 w-4 mr-2" />
                          Test Center: {result.testRegistration.testCenter}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white shadow rounded-lg p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Result Status</h2>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {getStatusIcon(result.status)}
                      <span className={`ml-3 px-3 py-1 rounded-full text-sm font-medium ${getStatusBadge(result.status)}`}>
                        {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
                      </span>
                    </div>
                  </div>
                  <div className="mt-4 text-sm text-gray-600">
                    <p>Result ID: {result.id}</p>
                    {result.certificateSerial && (
                      <p>Certificate Serial: {result.certificateSerial}</p>
                    )}
                    <p>Generated: {new Date(result.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>

                {result.overallBandScore && (
                  <div className="bg-gradient-to-br from-indigo-500 to-purple-600 shadow rounded-lg p-6 text-white text-center">
                    <h2 className="text-lg font-medium mb-4">Overall Band Score</h2>
                    <div className="text-5xl font-bold mb-2">
                      {result.overallBandScore}
                    </div>
                    <p className="text-indigo-100">IELTS Band Score</p>
                    <div className="mt-4 text-sm">
                      {result.overallBandScore >= 8.5 && <p>Expert User</p>}
                      {result.overallBandScore >= 7.5 && result.overallBandScore < 8.5 && <p>Very Good User</p>}
                      {result.overallBandScore >= 6.5 && result.overallBandScore < 7.5 && <p>Good User</p>}
                      {result.overallBandScore >= 5.5 && result.overallBandScore < 6.5 && <p>Modest User</p>}
                      {result.overallBandScore < 5.5 && <p>Limited User</p>}
                    </div>
                  </div>
                )}
              </div>

              {/* Right Column - Scores & Performance */}
              <div className="lg:col-span-2 space-y-6">
                {/* Score Charts */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                  <ScoreChart
                    scores={{
                      listening: result.listeningBandScore,
                      reading: result.readingBandScore,
                      writing: result.writingBandScore,
                      speaking: result.speakingBandScore,
                      overall: result.overallBandScore,
                    }}
                  />
                  <PerformanceChart
                    metrics={result.performanceMetrics}
                    overallScore={result.overallBandScore}
                  />
                </div>

                {/* Quick Actions */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Link
                      href={`/results/${resultId}/progress`}
                      className="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                    >
                      <Activity className="h-6 w-6 text-blue-600 mr-3" />
                      <div>
                        <div className="font-medium text-blue-900">View Progress</div>
                        <div className="text-sm text-blue-600">Module tracking</div>
                      </div>
                    </Link>
                    <Link
                      href={`/results/${resultId}/feedback`}
                      className="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
                    >
                      <MessageCircle className="h-6 w-6 text-green-600 mr-3" />
                      <div>
                        <div className="font-medium text-green-900">Get Feedback</div>
                        <div className="text-sm text-green-600">Detailed analysis</div>
                      </div>
                    </Link>
                    <Link
                      href={`/results/${resultId}/certificate`}
                      className="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
                    >
                      <Award className="h-6 w-6 text-purple-600 mr-3" />
                      <div>
                        <div className="font-medium text-purple-900">Certificate</div>
                        <div className="text-sm text-purple-600">Download PDF</div>
                      </div>
                    </Link>
                  </div>
                </div>

                {/* Quick Score Summary */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-6">Score Summary</h2>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {result.listeningBandScore && (
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <Headphones className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                        <p className="text-sm font-medium text-blue-800">Listening</p>
                        <p className="text-2xl font-bold text-blue-600">{result.listeningBandScore}</p>
                      </div>
                    )}
                    {result.readingBandScore && (
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <Eye className="h-6 w-6 text-green-600 mx-auto mb-2" />
                        <p className="text-sm font-medium text-green-800">Reading</p>
                        <p className="text-2xl font-bold text-green-600">{result.readingBandScore}</p>
                      </div>
                    )}
                    {result.writingBandScore && (
                      <div className="text-center p-4 bg-yellow-50 rounded-lg">
                        <PenTool className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
                        <p className="text-sm font-medium text-yellow-800">Writing</p>
                        <p className="text-2xl font-bold text-yellow-600">{result.writingBandScore}</p>
                      </div>
                    )}
                    {result.speakingBandScore && (
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <MessageCircle className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                        <p className="text-sm font-medium text-purple-800">Speaking</p>
                        <p className="text-2xl font-bold text-purple-600">{result.speakingBandScore}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-12 bg-white shadow rounded-lg p-6 text-center">
              <div className="flex items-center justify-center mb-4">
                <Award className="h-8 w-8 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Official IELTS Test Report</h3>
              </div>
              <p className="text-gray-600 mb-4">
                This is an official IELTS test result. For verification purposes, please use the result ID: {result.id}
              </p>
              {result.certificateSerial && (
                <p className="text-sm text-gray-500">
                  Certificate Serial Number: {result.certificateSerial}
                </p>
              )}
              <div className="mt-4 flex justify-center space-x-4">
                <Link
                  href="/search"
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  Search Other Results
                </Link>
                {result.certificateSerial && (
                  <Link
                    href={`/verify/${result.certificateSerial}`}
                    className="text-green-600 hover:text-green-700 text-sm font-medium"
                  >
                    Verify Certificate
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
